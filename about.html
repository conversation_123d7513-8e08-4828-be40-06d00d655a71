<!-- about.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="About Taif - Freelance IT Technician" />
  <title>About Me | Taif</title>
  <link rel="preload" href="me.jpeg" as="image">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap">
  <link rel="stylesheet" href="style.css" />
  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</head>
<body>
  <header class="navbar">
    <nav>
      <ul>
        <li><a href="index.html">Home</a></li>
        <li><a href="about.html" class="active">About</a></li>
        <li><a href="services.html">Services</a></li>
        <li><a href="projects.html">Projects</a></li>
        <li><a href="contact.html">Contact</a></li>
        <li><a href="cv.pdf" download><i class="fas fa-file-download"></i> CV</a></li>
        <li class="language-selector">
          <select id="languageSelect">
            <option value="en">🇺🇸 English</option>
            <option value="fr">🇫🇷 Français</option>
            <option value="ar">🇸🇦 العربية</option>
          </select>
        </li>
        <li><button id="modeToggle">🌙</button></li>
      </ul>
    </nav>
  </header>

  <main class="about-section">
    <section class="about-card">
      <img src="me.jpeg" alt="Taif" class="profile-pic" loading="lazy" decoding="async" width="150" height="150" />
      <h1>Hi, I'm Taif</h1>
      <p>I’m a passionate <strong>Freelance IT Technician & Web Developer</strong> from Tunis who loves helping people solve tech problems and create amazing web experiences. With over 2 years of experience, I specialize in helping individuals and small businesses with everything from PC troubleshooting to custom website development and cloud services. My mission is simple: to make technology work for you, not the other way around.</p>
      <p>I value clear communication, fast response times, and solutions that last. Whether you need quick remote support, on-site repairs, or ongoing IT maintenance, I’m ready to jump in and help you move forward.</p>
      <h2>Core Skills</h2>
      <ul class="skills-list">
        <li><i class="fas fa-tools"></i> Hardware & Software Troubleshooting</li>
        <li><i class="fas fa-network-wired"></i> Network Installation & Optimization</li>
        <li><i class="fas fa-lock"></i> IT Security & Backup Solutions</li>
        <li><i class="fas fa-cloud"></i> Cloud Service Integration (Google, Microsoft 365)</li>
        <li><i class="fas fa-user-cog"></i> Personalized IT Consulting</li>
        <li><i class="fas fa-code"></i> Web Development & Design</li>
        <li><i class="fas fa-mobile-alt"></i> Responsive & Mobile-First Design</li>
      </ul>
      <a href="cv.pdf" class="cta-button" download><i class="fas fa-file-download"></i> Download My CV</a>
    </section>
  </main>

  <footer>
    <p>&copy; 2025 Taif | <a href="mailto:<EMAIL>">Email Me</a></p>
  </footer>

  <script>
    const modeToggle = document.getElementById('modeToggle');
    modeToggle.onclick = () => {
      document.body.classList.toggle('dark-mode');
      modeToggle.textContent = document.body.classList.contains('dark-mode') ? '☀️' : '🌙';
    };

    // Language selector functionality
    const languageSelect = document.getElementById('languageSelect');
    languageSelect.onchange = () => {
      const selectedLang = languageSelect.value;
      localStorage.setItem('selectedLanguage', selectedLang);

      switch(selectedLang) {
        case 'fr':
          alert('Traduction française sera bientôt disponible!');
          break;
        case 'ar':
          alert('الترجمة العربية ستكون متاحة قريباً!');
          break;
        default:
          alert('Language changed to English');
      }
    };

    // Load saved language preference
    const savedLang = localStorage.getItem('selectedLanguage');
    if (savedLang) {
      languageSelect.value = savedLang;
    }

    // Optimize profile picture loading
    const profilePic = document.querySelector('.profile-pic');
    if (profilePic) {
      // Add loading placeholder
      profilePic.style.backgroundColor = '#f0f0f0';

      // Optimize image loading
      profilePic.addEventListener('load', function() {
        this.style.backgroundColor = 'transparent';
        this.style.opacity = '1';
      });

      // Set initial opacity for smooth fade-in
      profilePic.style.opacity = '0';
      profilePic.style.transition = 'opacity 0.3s ease';

      // Force load if already cached
      if (profilePic.complete) {
        profilePic.style.opacity = '1';
        profilePic.style.backgroundColor = 'transparent';
      }
    }
  </script>
</body>
</html>
