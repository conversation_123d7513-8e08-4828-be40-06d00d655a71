<!-- Alternative contact.html with EmailJS -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Contact Taif - Freelance IT Technician & Web Developer" />
  <title>Contact | Taif</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap">
  <link rel="stylesheet" href="style.css" />
  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
  <!-- EmailJS SDK -->
  <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
</head>
<body>
  <header class="navbar">
    <nav>
      <ul>
        <li><a href="index.html">Home</a></li>
        <li><a href="about.html">About</a></li>
        <li><a href="services.html">Services</a></li>
        <li><a href="projects.html">Projects</a></li>
        <li><a href="contact.html" class="active">Contact</a></li>
        <li><a href="cv.pdf" download><i class="fas fa-file-download"></i> CV</a></li>
        <li class="language-selector">
          <select id="languageSelect">
            <option value="en">🇺🇸 English</option>
            <option value="fr">🇫🇷 Français</option>
            <option value="ar">🇸🇦 العربية</option>
          </select>
        </li>
        <li><button id="modeToggle">🌙</button></li>
      </ul>
    </nav>
  </header>

  <main class="contact-section">
    <section class="contact-card">
      <h1>Get in Touch</h1>
      <p>If you'd like to work together or have any questions, feel free to send me a message below.</p>
      <form id="contactForm" class="contact-form">
        <input type="text" name="from_name" placeholder="Your Name" required>
        <input type="email" name="from_email" placeholder="Your Email" required>
        <textarea name="message" rows="5" placeholder="Your Message" required></textarea>
        <button type="submit" class="cta-button" id="submitBtn">Send Message</button>
        <div id="formStatus" style="margin-top: 1rem; display: none; padding: 1rem; border-radius: 10px; text-align: center;"></div>
      </form>
      <div class="contact-links">
        <p><i class="fas fa-envelope"></i> <EMAIL></p>
        <p><i class="fab fa-whatsapp"></i> <a href="https://wa.me/21600000000" target="_blank">+216 00 000 000</a></p>
        <p><i class="fab fa-linkedin"></i> <a href="https://linkedin.com/in/taif" target="_blank">LinkedIn</a></p>
      </div>
    </section>
  </main>

  <footer>
    <p>&copy; 2025 Taif | <a href="mailto:<EMAIL>">Email Me</a></p>
  </footer>

  <script>
    // Initialize EmailJS
    emailjs.init("YOUR_PUBLIC_KEY"); // Replace with your EmailJS public key

    const modeToggle = document.getElementById('modeToggle');
    modeToggle.onclick = () => {
      document.body.classList.toggle('dark-mode');
      modeToggle.textContent = document.body.classList.contains('dark-mode') ? '☀️' : '🌙';
    };

    // Language selector functionality
    const languageSelect = document.getElementById('languageSelect');
    languageSelect.onchange = () => {
      const selectedLang = languageSelect.value;
      localStorage.setItem('selectedLanguage', selectedLang);
      
      switch(selectedLang) {
        case 'fr':
          alert('Traduction française sera bientôt disponible!');
          break;
        case 'ar':
          alert('الترجمة العربية ستكون متاحة قريباً!');
          break;
        default:
          alert('Language changed to English');
      }
    };

    // Load saved language preference
    const savedLang = localStorage.getItem('selectedLanguage');
    if (savedLang) {
      languageSelect.value = savedLang;
    }

    // EmailJS Contact form handling
    const contactForm = document.getElementById('contactForm');
    const submitBtn = document.getElementById('submitBtn');
    const formStatus = document.getElementById('formStatus');

    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Show loading state
      submitBtn.textContent = 'Sending...';
      submitBtn.disabled = true;
      formStatus.style.display = 'none';
      
      // Send email using EmailJS
      emailjs.sendForm('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', this)
        .then(function() {
          // Success
          formStatus.style.display = 'block';
          formStatus.style.backgroundColor = '#d4edda';
          formStatus.style.color = '#155724';
          formStatus.style.border = '1px solid #c3e6cb';
          formStatus.innerHTML = '✅ Message sent successfully! I\'ll get back to you soon.';
          contactForm.reset();
        }, function(error) {
          // Error
          formStatus.style.display = 'block';
          formStatus.style.backgroundColor = '#f8d7da';
          formStatus.style.color = '#721c24';
          formStatus.style.border = '1px solid #f5c6cb';
          formStatus.innerHTML = '❌ Failed to send message. Please try again or email me directly.';
          console.log('EmailJS error:', error);
        })
        .finally(function() {
          // Reset button
          submitBtn.textContent = 'Send Message';
          submitBtn.disabled = false;
        });
    });
  </script>
</body>
</html>
