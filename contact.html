
<!-- contact.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Contact Taif - Freelance IT Technician & Web Developer" />
  <title>Contact | Taif</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap">
  <link rel="stylesheet" href="style.css" />
  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</head>
<body>
  <header class="navbar">
    <nav>
      <ul>
        <li><a href="index.html">Home</a></li>
        <li><a href="about.html">About</a></li>
        <li><a href="services.html">Services</a></li>
        <li><a href="projects.html">Projects</a></li>
        <li><a href="contact.html" class="active">Contact</a></li>
        <li><a href="cv.pdf" download><i class="fas fa-file-download"></i> CV</a></li>
        <li class="language-selector">
          <select id="languageSelect">
            <option value="en">🇺🇸 English</option>
            <option value="fr">🇫🇷 Français</option>
            <option value="ar">🇸🇦 العربية</option>
          </select>
        </li>
        <li><button id="modeToggle">🌙</button></li>
      </ul>
    </nav>
  </header>

  <main class="contact-section">
    <section class="contact-card">
      <h1>Get in Touch</h1>
      <p>If you'd like to work together or have any questions, feel free to send me a message below.</p>
      <form action="contact-handler.php" method="POST" class="contact-form" id="contactForm">
        <input type="text" name="name" placeholder="Your Name" required>
        <input type="email" name="email" placeholder="Your Email" required>
        <textarea name="message" rows="5" placeholder="Your Message" required></textarea>
        <button type="submit" class="cta-button" id="submitBtn">Send Message</button>
        <div id="formStatus" style="margin-top: 1rem; display: none; padding: 1rem; border-radius: 10px; text-align: center;"></div>
      </form>
      <div class="contact-links">
        <p><i class="fas fa-envelope"></i> <EMAIL></p>
        <p><i class="fab fa-whatsapp"></i> <a href="https://wa.me/21600000000" target="_blank">+216 00 000 000</a></p>
        <p><i class="fab fa-linkedin"></i> <a href="https://linkedin.com/in/taif" target="_blank">LinkedIn</a></p>
      </div>
    </section>
  </main>

  <footer>
    <p>&copy; 2025 Taif | <a href="mailto:<EMAIL>">Email Me</a></p>
  </footer>

  <script>
    const modeToggle = document.getElementById('modeToggle');
    modeToggle.onclick = () => {
      document.body.classList.toggle('dark-mode');
      modeToggle.textContent = document.body.classList.contains('dark-mode') ? '☀️' : '🌙';
    };

    // Language selector functionality
    const languageSelect = document.getElementById('languageSelect');
    languageSelect.onchange = () => {
      const selectedLang = languageSelect.value;
      localStorage.setItem('selectedLanguage', selectedLang);

      switch(selectedLang) {
        case 'fr':
          alert('Traduction française sera bientôt disponible!');
          break;
        case 'ar':
          alert('الترجمة العربية ستكون متاحة قريباً!');
          break;
        default:
          alert('Language changed to English');
      }
    };

    // Load saved language preference
    const savedLang = localStorage.getItem('selectedLanguage');
    if (savedLang) {
      languageSelect.value = savedLang;
    }

    // Contact form handling with AJAX
    const contactForm = document.getElementById('contactForm');
    const submitBtn = document.getElementById('submitBtn');
    const formStatus = document.getElementById('formStatus');

    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // Show loading state
      submitBtn.textContent = 'Sending...';
      submitBtn.disabled = true;
      formStatus.style.display = 'none';

      // Create FormData object
      const formData = new FormData(contactForm);

      // Send AJAX request
      fetch('contact-handler.php', {
        method: 'POST',
        body: formData
      })
      .then(response => response.text())
      .then(data => {
        formStatus.style.display = 'block';
        if (data.includes('successfully')) {
          formStatus.style.backgroundColor = '#d4edda';
          formStatus.style.color = '#155724';
          formStatus.style.border = '1px solid #c3e6cb';
          formStatus.innerHTML = '✅ ' + data;
          contactForm.reset();
        } else {
          formStatus.style.backgroundColor = '#f8d7da';
          formStatus.style.color = '#721c24';
          formStatus.style.border = '1px solid #f5c6cb';
          formStatus.innerHTML = '❌ ' + data;
        }
      })
      .catch(error => {
        formStatus.style.display = 'block';
        formStatus.style.backgroundColor = '#f8d7da';
        formStatus.style.color = '#721c24';
        formStatus.style.border = '1px solid #f5c6cb';
        formStatus.innerHTML = '❌ There was an error sending your message. Please try again.';
        console.error('Error:', error);
      })
      .finally(() => {
        submitBtn.textContent = 'Send Message';
        submitBtn.disabled = false;
      });
    });
  </script>
</body>
</html>

