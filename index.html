<!-- index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Freelance IT Technician Port<PERSON>lio - Taif." />
  <title>Taif | Freelance IT Technician & Web Developer</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap">
  <link rel="stylesheet" href="style.css" />
  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</head>
<body>
  <header class="navbar">
    <div class="logo">Taif</div>
    <nav>
      <ul>
        <li><a href="index.html">Home</a></li>
        <li><a href="about.html">About</a></li>
        <li><a href="services.html">Services</a></li>
        <li><a href="projects.html">Projects</a></li>
        <li><a href="contact.html">Contact</a></li>
        <li><a href="cv.pdf" download><i class="fas fa-file-download"></i> CV</a></li>
        <li><button id="modeToggle">🌙</button></li>
      </ul>
    </nav>
  </header>

  <main class="hero">
    <section class="intro-card">
      <h1>👋 Hi, I'm Taif</h1>
      <p>I provide IT solutions, computer repairs, network setup, and professional web development services for clients in Tunis and beyond. Whether you're a business or an individual, I'm here to help you stay connected, secure, and online.</p>
      <a href="contact.html" class="cta-button">Work with me</a>
    </section>
  </main>

  <footer>
    <p>&copy; 2025 Taif | <a href="mailto:<EMAIL>">Email Me</a></p>
  </footer>

  <script>
    const modeToggle = document.getElementById('modeToggle');
    modeToggle.onclick = () => {
      document.body.classList.toggle('dark-mode');
      modeToggle.textContent = document.body.classList.contains('dark-mode') ? '☀️' : '🌙';
    };
  </script>
</body>
</html>
