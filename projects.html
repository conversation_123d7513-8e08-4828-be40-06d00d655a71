<!-- projects.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Project Portfolio - Taif" />
  <title>Projects | Taif</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap">
  <link rel="stylesheet" href="style.css" />
  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</head>
<body>
  <header class="navbar">
    <nav>
      <ul>
        <li><a href="index.html">Home</a></li>
        <li><a href="about.html">About</a></li>
        <li><a href="services.html">Services</a></li>
        <li><a href="projects.html" class="active">Projects</a></li>
        <li><a href="contact.html">Contact</a></li>
        <li><a href="cv.pdf" download><i class="fas fa-file-download"></i> CV</a></li>
        <li class="language-selector">
          <select id="languageSelect">
            <option value="en">🇺🇸 English</option>
            <option value="fr">🇫🇷 Français</option>
            <option value="ar">🇸🇦 العربية</option>
          </select>
        </li>
        <li><button id="modeToggle">🌙</button></li>
      </ul>
    </nav>
  </header>

  <main class="projects-section">
    <section class="projects-card">
      <h1>My Work & Projects</h1>
      <p>Here are some recent IT and web development projects I've worked on. From hands-on tech support to custom websites, each reflects practical problem-solving and client satisfaction.</p>
      <div class="project-grid">
        <div class="project-item">
          <i class="fas fa-laptop"></i>
          <h3>Custom Office Network Setup</h3>
          <p>Installed secure LAN/Wi-Fi infrastructure for a 15-person office, optimized for VoIP and file sharing.</p>
        </div>
        <div class="project-item">
          <i class="fas fa-shield-alt"></i>
          <h3>Small Business Backup Strategy</h3>
          <p>Created a hybrid local/cloud backup solution for critical client data with automated daily syncs.</p>
        </div>
        <div class="project-item">
          <i class="fas fa-sync-alt"></i>
          <h3>Remote Maintenance for Freelancers</h3>
          <p>Provided monthly remote IT checks and support to independent professionals across Tunisia.</p>
        </div>
        <div class="project-item">
          <i class="fas fa-server"></i>
          <h3>Mini Server Deployment</h3>
          <p>Configured an Ubuntu-based local server for a graphic design agency to centralize file access.</p>
        </div>
        <div class="project-item">
          <i class="fas fa-cloud-upload-alt"></i>
          <h3>Cloud Transition</h3>
          <p>Moved a non-profit organization to Google Workspace with shared drives and collaborative tools.</p>
        </div>
        <div class="project-item">
          <i class="fas fa-desktop"></i>
          <h3>PC Refurbishment Program</h3>
          <p>Helped set up and distribute refurbished desktops to students in need with full OS/software stack.</p>
        </div>
        <div class="project-item">
          <i class="fas fa-globe"></i>
          <h3>E-commerce Website</h3>
          <p>Built a complete online store with payment integration, inventory management, and responsive design for a local business.</p>
        </div>
        <div class="project-item">
          <i class="fas fa-paint-brush"></i>
          <h3>Portfolio Website</h3>
          <p>Created a modern, responsive portfolio website for a photographer with image galleries and contact forms.</p>
        </div>
      </div>
    </section>
  </main>

  <footer>
    <p>&copy; 2025 Taif | <a href="mailto:<EMAIL>">Email Me</a></p>
  </footer>

  <script>
    const modeToggle = document.getElementById('modeToggle');
    modeToggle.onclick = () => {
      document.body.classList.toggle('dark-mode');
      modeToggle.textContent = document.body.classList.contains('dark-mode') ? '☀️' : '🌙';
    };

    // Language selector functionality
    const languageSelect = document.getElementById('languageSelect');
    languageSelect.onchange = () => {
      const selectedLang = languageSelect.value;
      localStorage.setItem('selectedLanguage', selectedLang);

      switch(selectedLang) {
        case 'fr':
          alert('Traduction française sera bientôt disponible!');
          break;
        case 'ar':
          alert('الترجمة العربية ستكون متاحة قريباً!');
          break;
        default:
          alert('Language changed to English');
      }
    };

    // Load saved language preference
    const savedLang = localStorage.getItem('selectedLanguage');
    if (savedLang) {
      languageSelect.value = savedLang;
    }
  </script>
</body>
</html>
