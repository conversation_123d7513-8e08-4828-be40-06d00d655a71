/* style.css */
body {
  margin: 0;
  font-family: 'Inter', sans-serif;
  background: #f8f9fa;
  color: #212529;
  transition: background 0.3s, color 0.3s;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #0d6efd;
  color: white;
}

.navbar .logo {
  font-weight: 800;
  font-size: 1.5rem;
}

.navbar ul {
  display: flex;
  list-style: none;
  gap: 1.2rem;
  padding: 0;
  margin: 0;
}

.navbar a {
  color: white;
  text-decoration: none;
  font-weight: 600;
}

.hero {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
  padding: 2rem;
  text-align: center;
  background: linear-gradient(to bottom right, #e9ecef, #ffffff);
}

.intro-card {
  max-width: 600px;
  padding: 2rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.intro-card h1 {
  margin-bottom: 1rem;
  font-size: 2.5rem;
}

.cta-button {
  display: inline-block;
  margin-top: 1.5rem;
  padding: 0.75rem 1.5rem;
  background: #0d6efd;
  color: white;
  border: none;
  border-radius: 10px;
  text-decoration: none;
  font-weight: 600;
  transition: background 0.3s;
}

.cta-button:hover {
  background: #0b5ed7;
}

footer {
  text-align: center;
  padding: 1rem;
  background: #212529;
  color: white;
}

.dark-mode {
  background-color: #121212;
  color: #f1f1f1;
}

.dark-mode .navbar {
  background-color: #1a1a1a;
}

.dark-mode .intro-card {
  background: #1e1e1e;
  box-shadow: none;
}

.dark-mode footer {
  background: #111;
}

button#modeToggle {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: white;
}

.dark-mode button#modeToggle {
  color: #f1f1f1;
}

.about-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem 2rem;
  background: linear-gradient(to bottom right, #f0f4f8, #ffffff);
  min-height: 100vh;
  text-align: center;
}

.about-card {
  max-width: 750px;
  background: white;
  padding: 3rem 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.profile-pic {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.skills-list {
  list-style: none;
  padding: 0;
  margin: 2rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.skills-list li {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.6rem;
  justify-content: center;
  font-size: 1rem;
}

.about-card h2 {
  margin-top: 2rem;
  font-size: 1.5rem;
}

.dark-mode .about-card {
  background: #1e1e1e;
  box-shadow: none;
}

.dark-mode .skills-list li {
  color: #ddd;
}

.services-section {
  padding: 4rem 2rem;
  background: linear-gradient(to bottom right, #f9fafc, #ffffff);
  min-height: 100vh;
  text-align: center;
}

.services-card {
  max-width: 1100px;
  margin: auto;
  background: white;
  padding: 3rem 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2.5rem;
}

.service-item {
  padding: 1.5rem;
  background: #f1f3f5;
  border-radius: 15px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.service-item:hover {
  transform: translateY(-5px);
}

.service-item i {
  font-size: 2rem;
  color: #0d6efd;
  margin-bottom: 1rem;
}

.service-item h3 {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.service-item p {
  font-size: 0.95rem;
  color: #555;
}

.dark-mode .services-card,
.dark-mode .service-item {
  background: #1e1e1e;
  color: #eee;
}

.dark-mode .service-item p {
  color: #ccc;
}

.projects-section {
  padding: 4rem 2rem;
  background: linear-gradient(to bottom right, #f0f4f8, #ffffff);
  min-height: 100vh;
  text-align: center;
}

.projects-card {
  max-width: 1100px;
  margin: auto;
  background: white;
  padding: 3rem 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2.5rem;
}

.project-item {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 15px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.project-item:hover {
  transform: translateY(-5px);
}

.project-item i {
  font-size: 2rem;
  color: #0d6efd;
  margin-bottom: 1rem;
}

.project-item h3 {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.project-item p {
  font-size: 0.95rem;
  color: #555;
}

.dark-mode .projects-card,
.dark-mode .project-item {
  background: #1e1e1e;
  color: #eee;
}

.dark-mode .project-item p {
  color: #ccc;
}

contact-section {
  padding: 4rem 2rem;
  background: linear-gradient(to bottom right, #f9fafc, #ffffff);
  min-height: 100vh;
  text-align: center;
}

.contact-card {
  max-width: 600px;
  margin: auto;
  background: white;
  padding: 3rem 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.contact-form input,
.contact-form textarea {
  padding: 0.75rem;
  border-radius: 10px;
  border: 1px solid #ccc;
  font-size: 1rem;
}

.contact-links {
  margin-top: 2rem;
  font-size: 1rem;
}

.contact-links p {
  margin: 0.5rem 0;
}

.contact-links a {
  color: #0d6efd;
  text-decoration: none;
}

.dark-mode .contact-card,
.dark-mode .contact-form input,
.dark-mode .contact-form textarea {
  background: #1e1e1e;
  color: #f1f1f1;
  border-color: #444;
}

.dark-mode .contact-links a {
  color: #66b2ff;
}

