/* style.css */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #212529;
  transition: all 0.3s ease;
  line-height: 1.6;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  color: #333;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar .logo {
  font-weight: 800;
  font-size: 1.8rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar ul {
  display: flex;
  list-style: none;
  gap: 1.5rem;
  padding: 0;
  margin: 0;
  align-items: center;
}

.navbar a {
  color: #333;
  text-decoration: none;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.navbar a:hover,
.navbar a.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2px);
}

.hero {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 90vh;
  padding: 2rem;
  text-align: center;
  position: relative;
}

.intro-card {
  max-width: 700px;
  padding: 3rem 2.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 25px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.intro-card h1 {
  margin-bottom: 1.5rem;
  font-size: 3rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.intro-card p {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 2rem;
}

.cta-button {
  display: inline-block;
  margin-top: 1.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.cta-button:active {
  transform: translateY(-1px);
}

footer {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  color: white;
  margin-top: 2rem;
}

footer a {
  color: #667eea;
  text-decoration: none;
  transition: color 0.3s ease;
}

footer a:hover {
  color: #764ba2;
}

.dark-mode {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: #f1f1f1;
}

.dark-mode .navbar {
  background: rgba(0, 0, 0, 0.9);
  color: #f1f1f1;
}

.dark-mode .navbar a {
  color: #f1f1f1;
}

.dark-mode .intro-card {
  background: rgba(0, 0, 0, 0.8);
  color: #f1f1f1;
}

.dark-mode .intro-card p {
  color: #ccc;
}

.dark-mode footer {
  background: rgba(0, 0, 0, 0.9);
}

button#modeToggle {
  background: rgba(102, 126, 234, 0.1);
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50px;
  font-size: 1.2rem;
  cursor: pointer;
  color: #333;
  padding: 0.5rem;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

button#modeToggle:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.1);
}

.dark-mode button#modeToggle {
  color: #f1f1f1;
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.about-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem 2rem;
  min-height: 100vh;
  text-align: center;
}

.about-card {
  max-width: 850px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  padding: 3.5rem 3rem;
  border-radius: 25px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.8s ease-out;
}

.about-card h1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
}

.about-card p {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 1.5rem;
}

.profile-pic {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  border: 4px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.profile-pic:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.skills-list {
  list-style: none;
  padding: 0;
  margin: 2.5rem 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.skills-list li {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: flex-start;
  font-size: 1.1rem;
  padding: 1rem 1.5rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 15px;
  transition: all 0.3s ease;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.skills-list li:hover {
  background: rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.skills-list li i {
  color: #667eea;
  font-size: 1.3rem;
}

.about-card h2 {
  margin-top: 3rem;
  margin-bottom: 1.5rem;
  font-size: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark-mode .about-card {
  background: rgba(0, 0, 0, 0.8);
  color: #f1f1f1;
}

.dark-mode .about-card p {
  color: #ccc;
}

.dark-mode .skills-list li {
  color: #ddd;
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .skills-list li:hover {
  background: rgba(255, 255, 255, 0.15);
}

.dark-mode .skills-list li i {
  color: #667eea;
}

.services-section {
  padding: 4rem 2rem;
  min-height: 100vh;
  text-align: center;
}

.services-card {
  max-width: 1200px;
  margin: auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  padding: 3.5rem 3rem;
  border-radius: 25px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.8s ease-out;
}

.services-card h1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
}

.services-card p {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 2rem;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2.5rem;
  margin-top: 3rem;
}

.service-item {
  padding: 2.5rem 2rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(102, 126, 234, 0.1);
  position: relative;
  overflow: hidden;
}

.service-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.service-item:hover::before {
  transform: scaleX(1);
}

.service-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.service-item i {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
  display: block;
}

.service-item h3 {
  margin-bottom: 1rem;
  font-size: 1.4rem;
  color: #333;
  font-weight: 700;
}

.service-item p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
}

.dark-mode .services-card {
  background: rgba(0, 0, 0, 0.8);
  color: #f1f1f1;
}

.dark-mode .services-card p {
  color: #ccc;
}

.dark-mode .service-item {
  background: rgba(255, 255, 255, 0.1);
  color: #eee;
  border-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .service-item h3 {
  color: #f1f1f1;
}

.dark-mode .service-item p {
  color: #ccc;
}

.projects-section {
  padding: 4rem 2rem;
  min-height: 100vh;
  text-align: center;
}

.projects-card {
  max-width: 1200px;
  margin: auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  padding: 3.5rem 3rem;
  border-radius: 25px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.8s ease-out;
}

.projects-card h1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
}

.projects-card p {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 2rem;
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2.5rem;
  margin-top: 3rem;
}

.project-item {
  padding: 2.5rem 2rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(102, 126, 234, 0.1);
  position: relative;
  overflow: hidden;
}

.project-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.project-item:hover::before {
  transform: scaleX(1);
}

.project-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.project-item i {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
  display: block;
}

.project-item h3 {
  margin-bottom: 1rem;
  font-size: 1.4rem;
  color: #333;
  font-weight: 700;
}

.project-item p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
}

.dark-mode .projects-card {
  background: rgba(0, 0, 0, 0.8);
  color: #f1f1f1;
}

.dark-mode .projects-card p {
  color: #ccc;
}

.dark-mode .project-item {
  background: rgba(255, 255, 255, 0.1);
  color: #eee;
  border-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .project-item h3 {
  color: #f1f1f1;
}

.dark-mode .project-item p {
  color: #ccc;
}

.contact-section {
  padding: 4rem 2rem;
  min-height: 100vh;
  text-align: center;
}

.contact-card {
  max-width: 700px;
  margin: auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  padding: 3.5rem 3rem;
  border-radius: 25px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.8s ease-out;
}

.contact-card h1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
}

.contact-card p {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 2rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2.5rem;
}

.contact-form input,
.contact-form textarea {
  padding: 1rem 1.5rem;
  border-radius: 15px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  font-size: 1rem;
  font-family: 'Inter', sans-serif;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.contact-links {
  margin-top: 3rem;
  font-size: 1.1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-links p {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 15px;
  transition: all 0.3s ease;
}

.contact-links p:hover {
  background: rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.contact-links i {
  color: #667eea;
  font-size: 1.2rem;
}

.contact-links a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.contact-links a:hover {
  color: #764ba2;
}

.dark-mode .contact-card {
  background: rgba(0, 0, 0, 0.8);
  color: #f1f1f1;
}

.dark-mode .contact-card p {
  color: #ccc;
}

.dark-mode .contact-form input,
.dark-mode .contact-form textarea {
  background: rgba(255, 255, 255, 0.1);
  color: #f1f1f1;
  border-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .contact-form input:focus,
.dark-mode .contact-form textarea:focus {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.15);
}

.dark-mode .contact-links p {
  background: rgba(255, 255, 255, 0.1);
  color: #f1f1f1;
}

.dark-mode .contact-links p:hover {
  background: rgba(255, 255, 255, 0.15);
}

.dark-mode .contact-links a {
  color: #667eea;
}

.dark-mode .contact-links a:hover {
  color: #764ba2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .navbar ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .intro-card h1,
  .about-card h1,
  .services-card h1,
  .projects-card h1,
  .contact-card h1 {
    font-size: 2rem;
  }

  .intro-card,
  .about-card,
  .services-card,
  .projects-card,
  .contact-card {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }

  .service-grid,
  .project-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .skills-list {
    grid-template-columns: 1fr;
  }
}

